import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ActiveFilters } from '@/modules/components/filters';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Card, Table } from '@/shared/components/common';
import ActionMenu from '@/shared/components/common/ActionMenu';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useBulkSelection } from '@/shared/hooks/useBulkSelection';
import useSlideForm from '@/shared/hooks/useSlideForm';

import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal';
import DepartmentForm from '../components/department/DepartmentForm';
import {
  useBulkDeleteDepartments,
  useCreateDepartment,
  useDepartments,
  useUpdateDepartment,
} from '../hooks/useDepartments';
import { DepartmentDto, DepartmentQueryDto } from '../types/department.types';

/**
 * Trang quản lý phòng ban
 */
const DepartmentsPage: React.FC = () => {
  const { t } = useTranslation(['hrm', 'common']);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // State để lưu trữ phòng ban đang được chỉnh sửa
  const [selectedDepartment, setSelectedDepartment] = useState<DepartmentDto | null>(null);

  // Hooks cho các thao tác CRUD
  const createDepartment = useCreateDepartment();
  const updateDepartment = useUpdateDepartment();
  const bulkDeleteDepartments = useBulkDeleteDepartments();

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<DepartmentDto>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '5%', sortable: true },
      {
        key: 'name',
        title: t('hrm:department.table.name', 'Tên phòng ban'),
        dataIndex: 'name',
        width: '25%',
        sortable: true,
      },
      {
        key: 'description',
        title: t('hrm:department.table.description', 'Mô tả'),
        dataIndex: 'description',
        width: '25%',
        render: (value: unknown) => {
          return <div>{value ? String(value) : '-'}</div>;
        },
      },
      {
        key: 'parentDepartment',
        title: t('hrm:department.table.parentDepartment', 'Phòng ban cấp trên'),
        dataIndex: 'parentDepartment',
        width: '20%',
        sortable: true,
        render: (_: unknown, record: DepartmentDto) => {
          return <div>{record.parentDepartment?.name || '-'}</div>;
        },
      },
      {
        key: 'manager',
        title: t('hrm:department.table.manager', 'Quản lý'),
        dataIndex: 'manager',
        width: '20%',
        sortable: true,
        render: (_: unknown, record: DepartmentDto) => {
          return <div>{record.manager?.fullName || '-'}</div>;
        },
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        width: '10%',
        render: (_: unknown, record: DepartmentDto) => (
          <ActionMenu
            showAllInMenu={true}
            menuIcon="more-horizontal"
            items={[
              {
                id: 'edit',
                label: t('common:edit', 'Sửa'),
                icon: 'edit',
                onClick: () => handleEdit(record),
              },
            ]}
          />
        ),
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): DepartmentQueryDto => {
    const queryParams: DepartmentQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue === 'noParent') {
      queryParams.parentId = null;
    } else if (params.filterValue === 'hasParent') {
      // Đây là một trường hợp đặc biệt, backend cần hỗ trợ lọc "có parent"
      // Có thể cần điều chỉnh tùy theo API
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<DepartmentDto, DepartmentQueryDto>({
      columns,
      filterOptions: [],
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách phòng ban với queryParams từ dataTable
  const { data: departmentsData, isLoading } = useDepartments(dataTable.queryParams);

  // Sử dụng custom hook để quản lý bulk selection
  const {
    selectedRowKeys,
    setSelectedRowKeys,
    showBulkDeleteConfirm,
    deleteCount,
    handleShowBulkDeleteConfirm,
    handleCancelBulkDelete,
    handleConfirmBulkDelete,
    isDeleting,
  } = useBulkSelection({
    data: departmentsData,
    bulkDeleteMutation: bulkDeleteDepartments,
  });

  // Xử lý thêm mới
  const handleAdd = () => {
    setSelectedDepartment(null);
    showForm();
  };

  // Xử lý chỉnh sửa
  const handleEdit = (department: DepartmentDto) => {
    setSelectedDepartment(department);
    showForm();
  };

  // Xử lý submit form
  const handleSubmit = async (values: Record<string, unknown>) => {
    try {
      if (selectedDepartment) {
        // Cập nhật phòng ban
        await updateDepartment.mutateAsync({
          id: selectedDepartment.id,
          data: values as any,
        });
      } else {
        // Tạo phòng ban mới
        await createDepartment.mutateAsync(values as any);
      }

      // Gọi hàm xử lý thành công
      handleFormSuccess();
    } catch (error) {
      console.error('Error submitting department form:', error);
    }
  };

  // Xử lý hủy form
  const handleCancel = () => {
    setSelectedDepartment(null);
    hideForm();
  };



  // Xử lý khi form submit thành công
  const handleFormSuccess = () => {
    setSelectedDepartment(null);
    hideForm();
  };

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      noParent: t('hrm:department.filter.noParent', 'Phòng ban cấp cao'),
      hasParent: t('hrm:department.filter.hasParent', 'Phòng ban con'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={false}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      <SlideInForm isVisible={isVisible}>
        <DepartmentForm
          initialData={selectedDepartment || undefined}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={createDepartment.isPending || updateDepartment.isPending}
        />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={departmentsData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: departmentsData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: departmentsData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete')}
        message={t(
          'hrm:department.bulkDeleteConfirmMessage',
          'Bạn có chắc chắn muốn xóa {{count}} phòng ban đã chọn?',
          { count: deleteCount }
        )}
        isSubmitting={isDeleting}
      />
    </div>
  );
};

export default DepartmentsPage;
